import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal } from './ui';
import { useAuth } from '../contexts/AuthContext';
import { formatDateTimeLocal12Hour } from '../utils/time';
import { API_BASE_URL } from '../config/api';

// Enhanced task data interfaces
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskCategory = 'question-creation' | 'project-delivery' | 'uploading' | 'quality-checking';
export type TaskStatus = 'active' | 'idle' | 'offline';

export interface TaskTag {
  id: string;
  label: string;
  color?: string;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  client_name?: string;
  status: string;
}

export interface EnhancedTaskData {
  task: string;
  status: TaskStatus;
  priority: TaskPriority;
  taskDurationMinutes: number;
  taskCategory: TaskCategory;
  progressPercentage: number;
  relatedProject: string;
  blockingIssues: string;
  tags: TaskTag[];
  expectedFinishDateTime: string; // Combined date and time field
  numberOfQuestions: number;
}

interface StatusUpdateFormProps {
  employeeId: string;
  currentTask: string;
  initialData?: Partial<EnhancedTaskData>;
  onSubmit: (taskData: EnhancedTaskData) => void;
  onCancel: () => void;
}

const StatusUpdateForm: React.FC<StatusUpdateFormProps> = ({
  employeeId,
  currentTask,
  initialData,
  onSubmit,
  onCancel
}) => {
  const { user } = useAuth();

  // Security check: Only allow users to update their own tasks
  if (!user || user.id !== employeeId) {
    return (
      <Modal
        isOpen={true}
        onClose={onCancel}
        title="Access Denied"
        size="sm"
      >
        <div className="text-center py-4">
          <p className="text-red-600 mb-4">You can only update your own tasks.</p>
          <Button variant="secondary" onClick={onCancel}>
            Close
          </Button>
        </div>
      </Modal>
    );
  }
  // Initialize form state with enhanced data structure
  const [formData, setFormData] = useState<EnhancedTaskData>({
    task: initialData?.task || currentTask,
    status: initialData?.status || 'active',
    priority: initialData?.priority || 'medium',
    taskDurationMinutes: initialData?.taskDurationMinutes || 30,
    taskCategory: initialData?.taskCategory || 'question-creation',
    progressPercentage: initialData?.progressPercentage || 0,
    relatedProject: initialData?.relatedProject || '',
    blockingIssues: initialData?.blockingIssues || '',
    tags: initialData?.tags || [],
    numberOfQuestions: initialData?.numberOfQuestions || 1,
    expectedFinishDateTime: initialData?.expectedFinishDateTime || ''
  });

  const [errors, setErrors] = useState<{
    task?: string;
    taskDurationMinutes?: string;
    numberOfQuestions?: string;
    expectedFinishDateTime?: string;
  }>({});

  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: {
      task?: string;
      taskDurationMinutes?: string;
      numberOfQuestions?: string;
      expectedFinishDateTime?: string;
    } = {};

    // Task validation
    if (!formData.task.trim()) {
      newErrors.task = 'Task description is required';
    } else if (formData.task.trim().length < 5) {
      newErrors.task = 'Task description must be at least 5 characters';
    }

    // Duration validation
    if (formData.taskDurationMinutes < 5 || formData.taskDurationMinutes > 480) {
      newErrors.taskDurationMinutes = 'Task duration must be between 5 minutes and 8 hours';
    }

    // Number of questions validation
    if (formData.numberOfQuestions < 1 || formData.numberOfQuestions > 1000) {
      newErrors.numberOfQuestions = 'Number of questions must be between 1 and 1000';
    }

    // Expected finish date/time validation
    if (formData.expectedFinishDateTime) {
      const selectedDateTime = new Date(formData.expectedFinishDateTime);
      const now = new Date();

      if (selectedDateTime <= now) {
        newErrors.expectedFinishDateTime = 'Expected finish time must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Submit the enhanced task data
    onSubmit({
      ...formData,
      task: formData.task.trim()
    });
  };

  // Helper function to update form data
  const updateFormData = (field: keyof EnhancedTaskData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear related errors when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoadingProjects(true);
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await fetch(`${API_BASE_URL}/api/projects`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && Array.isArray(data.data)) {
            setProjects(data.data);
          }
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setIsLoadingProjects(false);
      }
    };

    fetchProjects();
  }, []);

  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title="Update Your Status"
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Task Description */}
        <div>
          <label htmlFor="task" className="block text-sm font-medium text-neutral-700 mb-2">
            Current Task *
          </label>
          <textarea
            id="task"
            value={formData.task}
            onChange={(e) => updateFormData('task', e.target.value)}
            rows={3}
            className="input resize-none"
            placeholder="Describe what you're currently working on..."
          />
          {errors.task && (
            <p className="mt-2 text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {errors.task}
            </p>
          )}
        </div>

        {/* Priority and Status Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-neutral-700 mb-2">
              Priority Level
            </label>
            <select
              id="priority"
              value={formData.priority}
              onChange={(e) => updateFormData('priority', e.target.value as TaskPriority)}
              className="input"
            >
              <option value="low">🟢 Low - Can wait</option>
              <option value="medium">🟡 Medium - Normal priority</option>
              <option value="high">🟠 High - Important</option>
              <option value="urgent">🔴 Urgent - Critical</option>
            </select>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-neutral-700 mb-2">
              Status
            </label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => updateFormData('status', e.target.value as TaskStatus)}
              className="input"
            >
              <option value="active">🟢 Active - Currently working</option>
              <option value="idle">🟡 Idle - Available but not on task</option>
              <option value="offline">⚫ Offline - Away from work</option>
            </select>
          </div>
        </div>

          {/* Task Duration and Category Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="taskDurationMinutes" className="block text-sm font-medium text-neutral-700 mb-2">
                Task Duration (minutes) *
              </label>
              <select
                id="taskDurationMinutes"
                value={formData.taskDurationMinutes}
                onChange={(e) => updateFormData('taskDurationMinutes', parseInt(e.target.value))}
                className="input"
              >
                <option value={5}>5 minutes</option>
                <option value={10}>10 minutes</option>
                <option value={15}>15 minutes</option>
                <option value={30}>30 minutes</option>
                <option value={45}>45 minutes</option>
                <option value={60}>1 hour</option>
                <option value={90}>1.5 hours</option>
                <option value={120}>2 hours</option>
                <option value={180}>3 hours</option>
                <option value={240}>4 hours</option>
                <option value={300}>5 hours</option>
                <option value={360}>6 hours</option>
                <option value={420}>7 hours</option>
                <option value={480}>8 hours</option>
              </select>
              {errors.taskDurationMinutes && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {errors.taskDurationMinutes}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="taskCategory" className="block text-sm font-medium text-neutral-700 mb-2">
                Task Category
              </label>
              <select
                id="taskCategory"
                value={formData.taskCategory}
                onChange={(e) => updateFormData('taskCategory', e.target.value as TaskCategory)}
                className="input"
              >
                <option value="question-creation">❓ Question Creation</option>
                <option value="project-delivery">🚀 Project Delivery</option>
                <option value="uploading">📤 Uploading</option>
                <option value="quality-checking">✅ Quality Checking</option>
              </select>
            </div>
          </div>

        {/* Progress and Project Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="progressPercentage" className="block text-sm font-medium text-neutral-700 mb-2">
              Progress: {formData.progressPercentage}%
            </label>
            <div className="space-y-3">
              <input
                type="range"
                id="progressPercentage"
                min="0"
                max="100"
                step="5"
                value={formData.progressPercentage}
                onChange={(e) => updateFormData('progressPercentage', parseInt(e.target.value))}
                className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="w-full bg-neutral-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${formData.progressPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="relatedProject" className="block text-sm font-medium text-neutral-700 mb-2">
              Related Project/Client
            </label>
            <select
              id="relatedProject"
              value={formData.relatedProject}
              onChange={(e) => updateFormData('relatedProject', e.target.value)}
              className="input"
              disabled={isLoadingProjects}
            >
              <option value="">
                {isLoadingProjects ? 'Loading projects...' : 'Select a project (optional)'}
              </option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name} {project.client_name ? `- ${project.client_name}` : ''}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Number of Questions and Expected Finish Time Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="numberOfQuestions" className="block text-sm font-medium text-neutral-700 mb-2">
              Number of Questions
            </label>
            <input
              type="number"
              id="numberOfQuestions"
              min="1"
              max="1000"
              value={formData.numberOfQuestions}
              onChange={(e) => updateFormData('numberOfQuestions', parseInt(e.target.value) || 1)}
              className="input"
              placeholder="e.g., 50"
            />
            {errors.numberOfQuestions && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.numberOfQuestions}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="expectedFinishDateTime" className="block text-sm font-medium text-neutral-700 mb-2">
              Expected Finish Time
            </label>
            <input
              type="datetime-local"
              id="expectedFinishDateTime"
              value={formData.expectedFinishDateTime}
              onChange={(e) => updateFormData('expectedFinishDateTime', e.target.value)}
              className="input"
              min={new Date(Date.now() + 5 * 60000).toISOString().slice(0, 16)} // 5 minutes from now
            />
            {formData.expectedFinishDateTime && (
              <p className="mt-1 text-xs text-neutral-600">
                Selected: {formatDateTimeLocal12Hour(formData.expectedFinishDateTime)}
              </p>
            )}
            {errors.expectedFinishDateTime && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.expectedFinishDateTime}
              </p>
            )}
          </div>
        </div>

        {/* Blocking Issues and Tags Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="blockingIssues" className="block text-sm font-medium text-neutral-700 mb-2">
              Blocking Issues
            </label>
            <textarea
              id="blockingIssues"
              value={formData.blockingIssues}
              onChange={(e) => updateFormData('blockingIssues', e.target.value)}
              rows={3}
              className="input resize-none"
              placeholder="Any issues blocking progress..."
            />
          </div>

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-neutral-700 mb-2">
              Tags
            </label>
            <div className="space-y-2">
              <input
                type="text"
                id="tags"
                placeholder="Type a tag and press Enter"
                className="input"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const input = e.target as HTMLInputElement;
                    const tagText = input.value.trim();
                    if (tagText && !formData.tags.some(tag => tag.label === tagText)) {
                      const newTag: TaskTag = {
                        id: Date.now().toString(),
                        label: tagText,
                        color: `hsl(${Math.random() * 360}, 70%, 50%)`
                      };
                      updateFormData('tags', [...formData.tags, newTag]);
                      input.value = '';
                    }
                  }
                }}
              />
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag.label}
                      <button
                        type="button"
                        onClick={() => {
                          updateFormData('tags', formData.tags.filter(t => t.id !== tag.id));
                        }}
                        className="ml-1 text-blue-600 hover:text-blue-800 focus:outline-none"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-blue-800 mb-2">Quick Tips</h3>
              <div className="text-sm text-blue-700 grid grid-cols-1 md:grid-cols-2 gap-2">
                <p>• <strong>Priority:</strong> Help managers prioritize tasks</p>
                <p>• <strong>Progress:</strong> Track completion percentage</p>
                <p>• <strong>Duration:</strong> Estimate for workload planning</p>
                <p>• <strong>Tags:</strong> Add keywords for easy filtering</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            className="px-6"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            className="px-6"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
            }
          >
            Update Status
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default StatusUpdateForm;
