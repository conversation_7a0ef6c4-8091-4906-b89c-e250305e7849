import { Pool, PoolConfig } from 'pg';
import { config } from './index';
import { logger } from '@/utils/logger';

// Database configuration using DATABASE_URL (Railway standard)
const dbConfig: PoolConfig = {
  connectionString: config.db.connectionString,
  ssl: config.db.ssl ? { rejectUnauthorized: false } : false,
  max: config.db.maxConnections,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000, // Increased for Railway
  maxLifetimeSeconds: 300, // 5 minutes - helps with Railway connection management
};

// Create connection pool
export const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
  logger.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Database connection test with detailed logging
export const testConnection = async (): Promise<boolean> => {
  try {
    console.log('🔌 Testing database connection...');
    console.log('📋 Using DATABASE_URL connection string');

    const client = await pool.connect();
    console.log('✅ Database client connected');

    const result = await client.query('SELECT NOW(), version()');
    client.release();

    console.log('✅ Database query successful');
    console.log('📊 Database info:', {
      timestamp: result.rows[0]?.now,
      version: result.rows[0]?.version?.substring(0, 50) + '...'
    });

    logger.info('Database connection successful', {
      timestamp: result.rows[0]?.now
    });

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    logger.error('Database connection failed', error);
    return false;
  }
};

// Execute query with error handling
export const query = async (text: string, params?: any[]): Promise<any> => {
  const start = Date.now();
  
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Executed query', {
      query: text,
      duration: `${duration}ms`,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('Query execution failed', {
      query: text,
      params,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
};

// Transaction helper
export const transaction = async <T>(
  callback: (client: any) => Promise<T>
): Promise<T> => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Graceful shutdown
export const closePool = async (): Promise<void> => {
  try {
    await pool.end();
    logger.info('Database pool closed');
  } catch (error) {
    logger.error('Error closing database pool', error);
  }
};

// Health check query
export const healthCheck = async (): Promise<{
  status: 'healthy' | 'unhealthy';
  details: any;
}> => {
  try {
    const result = await query(`
      SELECT 
        COUNT(*) as total_connections,
        COUNT(CASE WHEN state = 'active' THEN 1 END) as active_connections,
        pg_database_size(current_database()) as database_size
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `);
    
    const stats = result.rows[0];
    
    return {
      status: 'healthy',
      details: {
        totalConnections: parseInt(stats.total_connections),
        activeConnections: parseInt(stats.active_connections),
        databaseSize: parseInt(stats.database_size),
        poolSize: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
};
