import express from 'express';
import { createServer } from 'http';
import compression from 'compression';
import fs from 'fs';
import path from 'path';
import { Server as SocketIOServer } from 'socket.io';
import { config, validateConfig } from '@/config';
import { testConnection, closePool } from '@/config/database';
import { logger, logStartup, logShutdown } from '@/utils/logger';
import { initializeDatabase, isDatabaseInitialized } from '@/utils/dbInit';
import {
  corsMiddleware,
  helmetMiddleware,
  rateLimitMiddleware,
  morganMiddleware,
  requestIdMiddleware,
  jsonParserMiddleware,
  urlEncodedMiddleware,
  notFoundMiddleware,
  errorHandlerMiddleware,
  healthCheckMiddleware,
  responseHelpersMiddleware
} from '@/middleware';

// Import routes
import authRoutes from '@/routes/auth';
import departmentRoutes from '@/routes/departments';
import taskRoutes from '@/routes/tasks';
import analyticsRoutes from '@/routes/analytics';
import projectRoutes from '@/routes/projects';

// Import services
import { initializeWebSocketService } from '@/services/websocketService';
import { taskScheduler } from './services/taskSchedulerService';

// =====================================================
// SERVER SETUP
// =====================================================

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: (origin, callback) => {
      // Allow requests with no origin
      if (!origin) return callback(null, true);

      // Get allowed origins from config
      const allowedOrigins = Array.isArray(config.websocket.corsOrigin)
        ? config.websocket.corsOrigin
        : [config.websocket.corsOrigin];

      // Check if the origin is allowed
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      // For development, allow localhost origins
      if (config.server.isDevelopment && origin.includes('localhost')) {
        return callback(null, true);
      }

      return callback(null, false);
    },
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// =====================================================
// MIDDLEWARE SETUP
// =====================================================

// Security middleware
app.use(helmetMiddleware);
app.use(corsMiddleware);

// Request processing middleware
app.use(requestIdMiddleware);
app.use(responseHelpersMiddleware);

// Parsing middleware
app.use(jsonParserMiddleware);
app.use(urlEncodedMiddleware);

// Compression
app.use(compression());

// Logging middleware (only in development or if explicitly enabled)
if (config.logging.enableMorgan) {
  app.use(morganMiddleware);
}

// Rate limiting
app.use(rateLimitMiddleware);

// =====================================================
// HEALTH CHECK ROUTES
// =====================================================

// Simple root endpoint for basic connectivity
app.get('/', (_req, res) => {
  res.json({
    success: true,
    message: 'Employee Task Dashboard API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: config.server.env
  });
});

app.get('/health', healthCheckMiddleware);
app.get('/api/health', healthCheckMiddleware);

// CORS debug endpoint (production safe)
app.get('/api/cors-test', (req, res) => {
  res.json({
    success: true,
    message: 'CORS test successful',
    origin: req.headers.origin,
    corsConfig: {
      allowedOrigin: config.cors.origin,
      credentials: config.cors.credentials
    },
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint for Railway deployment (only in development)
app.get('/debug/env', (_req, res) => {
  if (config.server.isProduction) {
    return res.status(404).json({ error: 'Not found' });
  }

  const dbVars = Object.keys(process.env)
    .filter(key => key.includes('PG') || key.includes('DB_') || key.includes('DATABASE'))
    .reduce((obj, key) => {
      obj[key] = key.includes('PASSWORD') ? '***' : (process.env[key] || '');
      return obj;
    }, {} as Record<string, string>);

  return res.json({
    nodeEnv: process.env['NODE_ENV'],
    port: process.env['PORT'],
    databaseVars: dbVars,
    hasRequiredVars: {
      pghost: !!process.env['PGHOST'],
      pgport: !!process.env['PGPORT'],
      pgdatabase: !!process.env['PGDATABASE'],
      pguser: !!process.env['PGUSER'],
      pgpassword: !!process.env['PGPASSWORD']
    }
  });
});

// Basic info endpoint
app.get('/', (_req, res) => {
  res.json({
    name: 'Employee Task Dashboard API',
    version: process.env['npm_package_version'] || '1.0.0',
    environment: config.server.env,
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// =====================================================
// API ROUTES
// =====================================================

app.use('/api/auth', authRoutes);
app.use('/api/departments', departmentRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/projects', projectRoutes);

// =====================================================
// WEBSOCKET SETUP
// =====================================================

// Initialize WebSocket service
const websocketService = initializeWebSocketService(io);

// Initialize Task Scheduler service
taskScheduler.start();

// =====================================================
// ERROR HANDLING
// =====================================================

// 404 handler
app.use(notFoundMiddleware);

// Global error handler
app.use(errorHandlerMiddleware);

// =====================================================
// SERVER STARTUP
// =====================================================

const startServer = async () => {
  try {
    console.log('🚀 Starting server initialization...');

    // Ensure required directories exist
    console.log('📁 Creating required directories...');
    const requiredDirs = [
      path.dirname(config.upload.uploadPath),
      path.dirname(config.logging.file)
    ];

    requiredDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      }
    });

    // Validate configuration
    console.log('🔧 Validating configuration...');
    validateConfig();
    logStartup('Configuration validated');

    // Test database connection
    console.log('🔌 Testing database connection...');
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ Database connection failed');
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection established');
    logStartup('Database connection established');

    // Initialize database tables automatically
    console.log('🔧 Checking database initialization...');
    try {
      const isInitialized = await isDatabaseInitialized();
      console.log('📊 Database initialized status:', isInitialized);

      if (!isInitialized) {
        console.log('🚀 Database not initialized, creating tables...');
        logStartup('Database not initialized, creating tables...');
        await initializeDatabase();
        console.log('✅ Database initialization completed successfully');
        logStartup('✅ Database initialization completed successfully');
      } else {
        console.log('✅ Database already initialized, skipping table creation');
        logStartup('Database already initialized, skipping table creation');
      }
    } catch (dbInitError) {
      console.error('❌ Database initialization failed:', dbInitError);
      logger.error('❌ Database initialization failed:', dbInitError);

      // In production, we should fail fast to let Railway restart
      if (config.server.isProduction) {
        console.error('💥 Exiting due to database initialization failure in production');
        throw dbInitError;
      } else {
        logger.warn('Continuing server startup despite database initialization failure');
        logger.warn('Database tables may need to be created manually');
      }
    }

    // Start server
    server.listen(config.server.port, config.server.host, () => {
      logStartup(`Server running on ${config.server.host}:${config.server.port}`, {
        environment: config.server.env,
        nodeVersion: process.version,
        pid: process.pid
      });
    });

    // Export io and websocket service for use in other modules
    (global as any).io = io;
    (global as any).websocketService = websocketService;

  } catch (error) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
};

// =====================================================
// GRACEFUL SHUTDOWN
// =====================================================

const gracefulShutdown = async (signal: string) => {
  logShutdown(`Received ${signal}, starting graceful shutdown`);

  // Close server
  server.close(() => {
    logShutdown('HTTP server closed');
  });

  // Close WebSocket connections
  io.close(() => {
    logShutdown('WebSocket server closed');
  });

  // Stop task scheduler
  taskScheduler.stop();
  logShutdown('Task scheduler stopped');

  // Close database connections
  await closePool();
  logShutdown('Database connections closed');

  logShutdown('Graceful shutdown completed');
  process.exit(0);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled promise rejection', { reason, promise });
  gracefulShutdown('UNHANDLED_REJECTION');
});

// Start the server
// Always start the server when this module is loaded
startServer();

export { app, server, io };
