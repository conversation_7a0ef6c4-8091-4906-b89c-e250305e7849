import { pool } from '@/config/database';
import { logger } from '@/utils/logger';

/**
 * Database migration script to add missing columns and fix schema issues
 * This script should be run when the database schema needs to be updated
 */

const migrationSQL = `
-- Add missing columns to task_updates table if they don't exist
DO $$ 
BEGIN
    -- Add number_of_questions column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'task_updates' AND column_name = 'number_of_questions') THEN
        ALTER TABLE task_updates ADD COLUMN number_of_questions INTEGER;
        RAISE NOTICE 'Added number_of_questions column to task_updates';
    END IF;

    -- Add expected_finish_datetime column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'task_updates' AND column_name = 'expected_finish_datetime') THEN
        ALTER TABLE task_updates ADD COLUMN expected_finish_datetime TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added expected_finish_datetime column to task_updates';
    END IF;
END $$;

-- Add missing columns to task_history table if they don't exist
DO $$ 
BEGIN
    -- Add number_of_questions column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'task_history' AND column_name = 'number_of_questions') THEN
        ALTER TABLE task_history ADD COLUMN number_of_questions INTEGER;
        RAISE NOTICE 'Added number_of_questions column to task_history';
    END IF;

    -- Add expected_finish_datetime column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'task_history' AND column_name = 'expected_finish_datetime') THEN
        ALTER TABLE task_history ADD COLUMN expected_finish_datetime TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added expected_finish_datetime column to task_history';
    END IF;
END $$;

-- Update task category constraints to match validation
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE table_name = 'task_updates' AND constraint_name = 'task_updates_category_check') THEN
        ALTER TABLE task_updates DROP CONSTRAINT task_updates_category_check;
        RAISE NOTICE 'Dropped old category constraint from task_updates';
    END IF;

    -- Add new constraint with correct categories
    ALTER TABLE task_updates ADD CONSTRAINT task_updates_category_check 
        CHECK (category IN ('question-creation', 'project-delivery', 'uploading', 'quality-checking'));
    RAISE NOTICE 'Added new category constraint to task_updates';
END $$;

-- Create task_update_tags table if it doesn't exist (rename from task_tag_assignments)
CREATE TABLE IF NOT EXISTS task_update_tags (
    task_update_id UUID REFERENCES task_updates(id) ON DELETE CASCADE,
    task_tag_id UUID REFERENCES task_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (task_update_id, task_tag_id)
);

-- Migrate data from task_tag_assignments to task_update_tags if needed
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'task_tag_assignments') THEN
        INSERT INTO task_update_tags (task_update_id, task_tag_id, created_at)
        SELECT task_update_id, tag_id, created_at 
        FROM task_tag_assignments
        ON CONFLICT DO NOTHING;
        
        DROP TABLE task_tag_assignments;
        RAISE NOTICE 'Migrated data from task_tag_assignments to task_update_tags';
    END IF;
END $$;

-- Add usage_count column to task_tags if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'task_tags' AND column_name = 'usage_count') THEN
        ALTER TABLE task_tags ADD COLUMN usage_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added usage_count column to task_tags';
        
        -- Update usage count based on existing data
        UPDATE task_tags SET usage_count = (
            SELECT COUNT(*) FROM task_update_tags WHERE task_tag_id = task_tags.id
        );
        RAISE NOTICE 'Updated usage_count values';
    END IF;
END $$;

-- Update task_tags name column length if needed
DO $$
BEGIN
    -- Check current column length
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'task_tags' AND column_name = 'name' 
               AND character_maximum_length < 100) THEN
        ALTER TABLE task_tags ALTER COLUMN name TYPE VARCHAR(100);
        RAISE NOTICE 'Updated task_tags name column to VARCHAR(100)';
    END IF;
END $$;
`;

export const migrateDatabase = async (): Promise<void> => {
  let client;

  try {
    console.log('🚀 Starting database migration...');
    logger.info('Starting database migration...');

    client = await pool.connect();
    logger.info('Database connection established for migration');

    // Start transaction
    await client.query('BEGIN');
    logger.info('Migration transaction started');

    // Run migration SQL
    console.log('🔧 Running migration SQL...');
    logger.info('Running migration SQL...');
    
    await client.query(migrationSQL);
    
    console.log('✅ Migration SQL executed successfully');
    logger.info('Migration SQL executed successfully');

    // Commit transaction
    console.log('🔧 Committing migration transaction...');
    await client.query('COMMIT');
    console.log('✅ Migration transaction committed successfully');
    logger.info('Migration transaction committed successfully');

    console.log('✅ Database migration completed successfully');
    logger.info('Database migration completed successfully');

  } catch (error) {
    logger.error('❌ Database migration failed:', error);

    if (client) {
      try {
        await client.query('ROLLBACK');
        logger.info('Migration transaction rolled back');
      } catch (rollbackError) {
        logger.error('Failed to rollback migration transaction:', rollbackError);
      }
    }

    throw error;
  } finally {
    if (client) {
      client.release();
      logger.info('Database client released');
    }
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  migrateDatabase()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
