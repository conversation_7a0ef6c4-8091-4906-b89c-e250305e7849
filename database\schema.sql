-- Employee Task Dashboard - PostgreSQL Schema
-- Designed for scalability and analytics
-- Version: 1.0

-- Enable UUID extension for better performance and security
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pg_stat_statements for query performance monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Departments table
CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID, -- Self-referencing to users table
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Users table (employees)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(id),
    role VARCHAR(50) DEFAULT 'employee', -- employee, manager, admin
    hire_date DATE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    avatar_url TEXT,
    phone VARCHAR(20),
    emergency_contact JSONB, -- Flexible structure for emergency contact info
    skills JSONB, -- Array of skills for analytics
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Projects table for better organization
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    client_name VARCHAR(255),
    department_id UUID REFERENCES departments(id),
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'active', -- active, completed, on_hold, cancelled
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent
    budget DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Task updates table (current status)
CREATE TABLE task_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    task_description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'idle', 'offline')),
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('question-creation', 'project-delivery', 'uploading', 'quality-checking')),
    estimated_duration_minutes INTEGER, -- Stored in minutes for easy calculations
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    project_id UUID REFERENCES projects(id),
    expected_completion_date DATE,
    blocking_issues TEXT,
    number_of_questions INTEGER,
    expected_finish_datetime TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one active task per user
    CONSTRAINT unique_active_task_per_user UNIQUE (user_id) DEFERRABLE INITIALLY DEFERRED
);

-- Task history table (audit trail)
CREATE TABLE task_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    task_update_id UUID REFERENCES task_updates(id),
    task_description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    estimated_duration_minutes INTEGER,
    progress_percentage INTEGER,
    project_id UUID REFERENCES projects(id),
    expected_completion_date DATE,
    blocking_issues TEXT,
    number_of_questions INTEGER,
    expected_finish_datetime TIMESTAMP WITH TIME ZONE,
    action_type VARCHAR(20) NOT NULL CHECK (action_type IN ('created', 'updated', 'completed', 'cancelled')),
    session_duration_minutes INTEGER, -- How long they worked on this task
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for analytics queries
    INDEX idx_task_history_user_date (user_id, created_at),
    INDEX idx_task_history_category_date (category, created_at),
    INDEX idx_task_history_project_date (project_id, created_at)
);

-- Task tags table (many-to-many relationship)
CREATE TABLE task_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    color VARCHAR(7), -- Hex color code
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 0 -- For analytics
);

-- Junction table for task-tag relationships
CREATE TABLE task_update_tags (
    task_update_id UUID NOT NULL REFERENCES task_updates(id) ON DELETE CASCADE,
    task_tag_id UUID NOT NULL REFERENCES task_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (task_update_id, task_tag_id)
);

-- User sessions for analytics
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    session_start TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50), -- desktop, mobile, tablet
    browser VARCHAR(100),
    total_active_time_minutes INTEGER, -- Calculated active time
    tasks_completed INTEGER DEFAULT 0,
    status_updates_count INTEGER DEFAULT 0
);

-- =====================================================
-- ANALYTICS TABLES (Pre-aggregated for performance)
-- =====================================================

-- Daily productivity summary (materialized for fast dashboard loading)
CREATE TABLE daily_productivity_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    department_id UUID NOT NULL REFERENCES departments(id),
    date DATE NOT NULL,
    total_active_minutes INTEGER DEFAULT 0,
    total_idle_minutes INTEGER DEFAULT 0,
    total_offline_minutes INTEGER DEFAULT 0,
    tasks_completed INTEGER DEFAULT 0,
    tasks_started INTEGER DEFAULT 0,
    status_updates_count INTEGER DEFAULT 0,
    avg_task_completion_time_minutes DECIMAL(10,2),
    high_priority_tasks INTEGER DEFAULT 0,
    urgent_priority_tasks INTEGER DEFAULT 0,
    categories_worked JSONB, -- Array of categories worked on
    projects_worked JSONB, -- Array of projects worked on
    productivity_score DECIMAL(5,2), -- Calculated productivity metric
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, date)
);

-- Department metrics (aggregated daily)
CREATE TABLE department_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    department_id UUID NOT NULL REFERENCES departments(id),
    date DATE NOT NULL,
    total_employees INTEGER,
    active_employees INTEGER,
    idle_employees INTEGER,
    offline_employees INTEGER,
    total_tasks_completed INTEGER DEFAULT 0,
    avg_productivity_score DECIMAL(5,2),
    total_active_hours DECIMAL(10,2),
    urgent_tasks_count INTEGER DEFAULT 0,
    blocked_tasks_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(department_id, date)
);

-- Task performance metrics
CREATE TABLE task_performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    department_id UUID REFERENCES departments(id),
    date DATE NOT NULL,
    total_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    avg_completion_time_minutes DECIMAL(10,2),
    avg_estimated_vs_actual_ratio DECIMAL(5,2), -- How accurate estimates are
    blocked_tasks_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(category, priority, department_id, date)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Users table indexes
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- Task updates indexes
CREATE INDEX idx_task_updates_user_id ON task_updates(user_id);
CREATE INDEX idx_task_updates_status ON task_updates(status);
CREATE INDEX idx_task_updates_priority ON task_updates(priority);
CREATE INDEX idx_task_updates_category ON task_updates(category);
CREATE INDEX idx_task_updates_project_id ON task_updates(project_id);
CREATE INDEX idx_task_updates_created_at ON task_updates(created_at);
CREATE INDEX idx_task_updates_completion_date ON task_updates(expected_completion_date);

-- Task history indexes (critical for analytics)
CREATE INDEX idx_task_history_user_date ON task_history(user_id, created_at);
CREATE INDEX idx_task_history_category_date ON task_history(category, created_at);
CREATE INDEX idx_task_history_priority_date ON task_history(priority, created_at);
CREATE INDEX idx_task_history_project_date ON task_history(project_id, created_at);
CREATE INDEX idx_task_history_action_type ON task_history(action_type);

-- Projects indexes
CREATE INDEX idx_projects_department_id ON projects(department_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_priority ON projects(priority);
CREATE INDEX idx_projects_dates ON projects(start_date, end_date);

-- User sessions indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_start_time ON user_sessions(session_start);
CREATE INDEX idx_user_sessions_device_type ON user_sessions(device_type);

-- Analytics tables indexes
CREATE INDEX idx_daily_productivity_user_date ON daily_productivity_summary(user_id, date);
CREATE INDEX idx_daily_productivity_dept_date ON daily_productivity_summary(department_id, date);
CREATE INDEX idx_daily_productivity_score ON daily_productivity_summary(productivity_score);

CREATE INDEX idx_department_metrics_dept_date ON department_metrics(department_id, date);
CREATE INDEX idx_department_metrics_date ON department_metrics(date);

CREATE INDEX idx_task_performance_category_date ON task_performance_metrics(category, date);
CREATE INDEX idx_task_performance_dept_date ON task_performance_metrics(department_id, date);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_updates_updated_at BEFORE UPDATE ON task_updates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_productivity_updated_at BEFORE UPDATE ON daily_productivity_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create task history entry when task is updated
CREATE OR REPLACE FUNCTION create_task_history_entry()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate session duration if this is an update
    DECLARE
        session_duration INTEGER := NULL;
        last_update TIMESTAMP WITH TIME ZONE;
    BEGIN
        IF TG_OP = 'UPDATE' THEN
            -- Get the last update time for this user
            SELECT created_at INTO last_update
            FROM task_history
            WHERE user_id = NEW.user_id
            ORDER BY created_at DESC
            LIMIT 1;

            IF last_update IS NOT NULL THEN
                session_duration := EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_update)) / 60;
            END IF;
        END IF;

        -- Insert into task history
        INSERT INTO task_history (
            user_id,
            task_update_id,
            task_description,
            status,
            priority,
            category,
            estimated_duration_minutes,
            progress_percentage,
            project_id,
            expected_completion_date,
            blocking_issues,
            action_type,
            session_duration_minutes
        ) VALUES (
            NEW.user_id,
            NEW.id,
            NEW.task_description,
            NEW.status,
            NEW.priority,
            NEW.category,
            NEW.estimated_duration_minutes,
            NEW.progress_percentage,
            NEW.project_id,
            NEW.expected_completion_date,
            NEW.blocking_issues,
            CASE
                WHEN TG_OP = 'INSERT' THEN 'created'
                WHEN TG_OP = 'UPDATE' THEN 'updated'
                ELSE 'unknown'
            END,
            session_duration
        );

        RETURN NEW;
    END;
END;
$$ language 'plpgsql';

-- Apply task history trigger
CREATE TRIGGER create_task_history_on_insert_update
    AFTER INSERT OR UPDATE ON task_updates
    FOR EACH ROW EXECUTE FUNCTION create_task_history_entry();

-- Function to update tag usage count
CREATE OR REPLACE FUNCTION update_tag_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE task_tags
        SET usage_count = usage_count + 1
        WHERE id = NEW.task_tag_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE task_tags
        SET usage_count = GREATEST(usage_count - 1, 0)
        WHERE id = OLD.task_tag_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Apply tag usage trigger
CREATE TRIGGER update_tag_usage_on_change
    AFTER INSERT OR DELETE ON task_update_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add foreign key for department manager
ALTER TABLE departments
ADD CONSTRAINT fk_departments_manager
FOREIGN KEY (manager_id) REFERENCES users(id);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Current employee status view (for dashboard)
CREATE VIEW current_employee_status AS
SELECT
    u.id as user_id,
    u.name,
    u.email,
    d.name as department_name,
    d.id as department_id,
    tu.task_description,
    tu.status,
    tu.priority,
    tu.category,
    tu.progress_percentage,
    p.name as project_name,
    tu.expected_completion_date,
    tu.blocking_issues,
    tu.updated_at as last_updated,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 as minutes_since_update
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN task_updates tu ON u.id = tu.user_id
LEFT JOIN projects p ON tu.project_id = p.id
WHERE u.is_active = true;

-- Department summary view
CREATE VIEW department_summary AS
SELECT
    d.id,
    d.name,
    d.description,
    COUNT(u.id) as total_employees,
    COUNT(CASE WHEN tu.status = 'active' THEN 1 END) as active_employees,
    COUNT(CASE WHEN tu.status = 'idle' THEN 1 END) as idle_employees,
    COUNT(CASE WHEN tu.status = 'offline' THEN 1 END) as offline_employees,
    COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
    COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks
FROM departments d
LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
LEFT JOIN task_updates tu ON u.id = tu.user_id
WHERE d.is_active = true
GROUP BY d.id, d.name, d.description;
