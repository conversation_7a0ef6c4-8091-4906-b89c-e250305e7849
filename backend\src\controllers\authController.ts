import { Request, Response } from 'express';
import { query, transaction } from '@/config/database';
import {
  hashPassword,
  verifyPassword,
  generateAccessToken,
  generateRefreshToken,
  verifyRefreshToken,
  validatePasswordStrength,
  validateEmail,
  validateEmailDomain,
  getTokenExpiration
} from '@/utils/auth';
import { logger, logAuth } from '@/utils/logger';
import { User } from '@/types/database';
import { 
  LoginRequest, 
  SignupRequest, 
  AuthResponse, 
  RefreshTokenRequest,
  ChangePasswordRequest,
  ApiResponse 
} from '@/types/api';

// =====================================================
// AUTHENTICATION CONTROLLERS
// =====================================================

// Login user
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password }: LoginRequest = req.body;

    // Validate input
    if (!email || !password) {
      res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
      return;
    }

    if (!validateEmail(email)) {
      res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
      return;
    }

    // Find user by email
    const result = await query(
      'SELECT * FROM users WHERE email = $1 AND is_active = true',
      [email.toLowerCase()]
    );

    if (result.rows.length === 0) {
      logAuth('login_failed', undefined, { email, reason: 'user_not_found' });
      res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
      return;
    }

    const user = result.rows[0] as User;

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      logAuth('login_failed', user.id, { email, reason: 'invalid_password' });
      res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
      return;
    }

    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user.id);
    const expiresAt = getTokenExpiration(accessToken);

    // Update last login
    await query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    // Get department name
    const deptResult = await query(
      'SELECT name FROM departments WHERE id = $1',
      [user.department_id]
    );

    logAuth('login_success', user.id, { email: user.email });

    const response: AuthResponse = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        department_id: user.department_id,
        role: user.role,
        timezone: user.timezone,
        avatar_url: user.avatar_url
      },
      token: accessToken,
      refreshToken,
      expiresAt: expiresAt?.toISOString() || ''
    };

    res.json({
      success: true,
      data: response,
      message: 'Login successful'
    });
  } catch (error) {
    logger.error('Login error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Register new user
export const signup = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      name, 
      email, 
      password, 
      department_id, 
      phone, 
      timezone 
    }: SignupRequest = req.body;

    // Validate input
    if (!name || !email || !password || !department_id) {
      res.status(400).json({
        success: false,
        message: 'Name, email, password, and department are required'
      });
      return;
    }

    if (!validateEmail(email)) {
      res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
      return;
    }

    if (!validateEmailDomain(email)) {
      res.status(400).json({
        success: false,
        message: 'Only @imocha.io email addresses are allowed',
        errors: [{
          field: 'email',
          message: 'Only @imocha.io email addresses are allowed',
          code: 'INVALID_DOMAIN'
        }]
      });
      return;
    }

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      res.status(400).json({
        success: false,
        message: 'Password does not meet requirements',
        errors: passwordValidation.errors.map(error => ({
          field: 'password',
          message: error,
          code: 'WEAK_PASSWORD'
        }))
      });
      return;
    }

    // Check if department exists
    const deptResult = await query(
      'SELECT id FROM departments WHERE id = $1 AND is_active = true',
      [department_id]
    );

    if (deptResult.rows.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Invalid department'
      });
      return;
    }

    // Use transaction for user creation
    const user = await transaction(async (client) => {
      // Check if email already exists
      const existingUser = await client.query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUser.rows.length > 0) {
        throw new Error('EMAIL_EXISTS');
      }

      // Hash password
      const hashedPassword = await hashPassword(password);

      // Create user
      const userResult = await client.query(`
        INSERT INTO users (
          email, name, password_hash, department_id, role, 
          timezone, phone, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [
        email.toLowerCase(),
        name.trim(),
        hashedPassword,
        department_id,
        'employee', // Default role
        timezone || 'UTC',
        phone || null
      ]);

      return userResult.rows[0] as User;
    });

    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user.id);
    const expiresAt = getTokenExpiration(accessToken);

    logAuth('signup_success', user.id, { email: user.email });

    const response: AuthResponse = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        department_id: user.department_id,
        role: user.role,
        timezone: user.timezone,
        avatar_url: user.avatar_url
      },
      token: accessToken,
      refreshToken,
      expiresAt: expiresAt?.toISOString() || ''
    };

    res.status(201).json({
      success: true,
      data: response,
      message: 'Account created successfully'
    });
  } catch (error) {
    if (error instanceof Error && error.message === 'EMAIL_EXISTS') {
      res.status(409).json({
        success: false,
        message: 'Email already registered',
        errors: [{
          field: 'email',
          message: 'This email is already registered',
          code: 'EMAIL_EXISTS'
        }]
      });
      return;
    }

    logger.error('Signup error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Refresh access token
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken }: RefreshTokenRequest = req.body;

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: 'Refresh token required'
      });
      return;
    }

    // Verify refresh token
    const payload = verifyRefreshToken(refreshToken);
    if (!payload) {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token'
      });
      return;
    }

    // Get user
    const result = await query(
      'SELECT * FROM users WHERE id = $1 AND is_active = true',
      [payload.userId]
    );

    if (result.rows.length === 0) {
      res.status(401).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    const user = result.rows[0] as User;

    // Generate new tokens
    const newAccessToken = generateAccessToken(user);
    const newRefreshToken = generateRefreshToken(user.id);
    const expiresAt = getTokenExpiration(newAccessToken);

    logAuth('token_refreshed', user.id);

    res.json({
      success: true,
      data: {
        token: newAccessToken,
        refreshToken: newRefreshToken,
        expiresAt: expiresAt?.toISOString() || ''
      },
      message: 'Token refreshed successfully'
    });
  } catch (error) {
    logger.error('Token refresh error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Change password
export const changePassword = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const { currentPassword, newPassword }: ChangePasswordRequest = req.body;

    if (!currentPassword || !newPassword) {
      res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
      return;
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, req.user.password_hash);
    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
      return;
    }

    // Validate new password strength
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      res.status(400).json({
        success: false,
        message: 'New password does not meet requirements',
        errors: passwordValidation.errors.map(error => ({
          field: 'newPassword',
          message: error,
          code: 'WEAK_PASSWORD'
        }))
      });
      return;
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await query(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [hashedNewPassword, req.user.id]
    );

    logAuth('password_changed', req.user.id);

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Change password error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Logout (client-side token invalidation)
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    if (req.user) {
      logAuth('logout', req.user.id);
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    logger.error('Logout error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get current user profile
export const getProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    // Get department name
    const deptResult = await query(
      'SELECT name FROM departments WHERE id = $1',
      [req.user.department_id]
    );

    const departmentName = deptResult.rows[0]?.name || 'Unknown';

    const profile = {
      id: req.user.id,
      name: req.user.name,
      email: req.user.email,
      department_id: req.user.department_id,
      department_name: departmentName,
      role: req.user.role,
      hire_date: req.user.hire_date?.toISOString(),
      timezone: req.user.timezone,
      avatar_url: req.user.avatar_url,
      phone: req.user.phone,
      emergency_contact: req.user.emergency_contact,
      skills: req.user.skills,
      last_login_at: req.user.last_login_at?.toISOString(),
      created_at: req.user.created_at.toISOString()
    };

    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    logger.error('Get profile error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// =====================================================
// PUBLIC ENDPOINTS
// =====================================================

// Get departments for signup form (public endpoint)
export const getPublicDepartments = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get only active departments with basic info for signup
    const result = await query(`
      SELECT
        id,
        name,
        description
      FROM departments
      WHERE is_active = true
      ORDER BY name ASC
    `);

    const departments = result.rows.map(dept => ({
      id: dept.id,
      name: dept.name,
      description: dept.description
    }));

    res.json({
      success: true,
      data: departments
    });

    logger.info('Public departments fetched successfully', {
      count: departments.length
    });

  } catch (error) {
    logger.error('Get public departments error', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch departments'
    });
  }
};
