// TypeScript interfaces matching the database schema
// Use these types in your frontend application for type safety

// =====================================================
// ENUMS AND CONSTANTS
// =====================================================

export type TaskStatus = 'active' | 'idle' | 'offline';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskCategory =
  | 'question-creation'
  | 'project-delivery'
  | 'uploading'
  | 'quality-checking';

export type UserRole = 'employee' | 'manager' | 'admin';
export type ProjectStatus = 'active' | 'completed' | 'on_hold' | 'cancelled';
export type TaskActionType = 'created' | 'updated' | 'completed' | 'cancelled';

// =====================================================
// CORE INTERFACES
// =====================================================

export interface Department {
  id: string;
  name: string;
  description?: string;
  manager_id?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface User {
  id: string;
  email: string;
  name: string;
  password_hash: string;
  department_id: string;
  role: UserRole;
  hire_date?: string;
  timezone: string;
  avatar_url?: string;
  phone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  is_active: boolean;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  client_name?: string;
  department_id?: string;
  start_date?: string;
  end_date?: string;
  status: ProjectStatus;
  priority: TaskPriority;
  budget?: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface TaskUpdate {
  id: string;
  user_id: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage: number;
  project_id?: string;
  expected_completion_date?: string;
  blocking_issues?: string;
  created_at: string;
  updated_at: string;
}

export interface TaskHistory {
  id: string;
  user_id: string;
  task_update_id?: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage?: number;
  project_id?: string;
  expected_completion_date?: string;
  blocking_issues?: string;
  action_type: TaskActionType;
  session_duration_minutes?: number;
  created_at: string;
}

export interface TaskTag {
  id: string;
  name: string;
  color?: string;
  description?: string;
  created_at: string;
  usage_count: number;
}

export interface TaskUpdateTag {
  task_update_id: string;
  task_tag_id: string;
  created_at: string;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_start: string;
  session_end?: string;
  ip_address?: string;
  user_agent?: string;
  device_type?: string;
  browser?: string;
  total_active_time_minutes?: number;
  tasks_completed: number;
  status_updates_count: number;
}

// =====================================================
// ANALYTICS INTERFACES
// =====================================================

export interface DailyProductivitySummary {
  id: string;
  user_id: string;
  department_id: string;
  date: string;
  total_active_minutes: number;
  total_idle_minutes: number;
  total_offline_minutes: number;
  tasks_completed: number;
  tasks_started: number;
  status_updates_count: number;
  avg_task_completion_time_minutes?: number;
  high_priority_tasks: number;
  urgent_priority_tasks: number;
  categories_worked?: string[];
  projects_worked?: string[];
  productivity_score?: number;
  created_at: string;
  updated_at: string;
}

export interface DepartmentMetrics {
  id: string;
  department_id: string;
  date: string;
  total_employees: number;
  active_employees: number;
  idle_employees: number;
  offline_employees: number;
  total_tasks_completed: number;
  avg_productivity_score?: number;
  total_active_hours?: number;
  urgent_tasks_count: number;
  blocked_tasks_count: number;
  created_at: string;
}

export interface TaskPerformanceMetrics {
  id: string;
  category: TaskCategory;
  priority: TaskPriority;
  department_id?: string;
  date: string;
  total_tasks: number;
  completed_tasks: number;
  avg_completion_time_minutes?: number;
  avg_estimated_vs_actual_ratio?: number;
  blocked_tasks_count: number;
  created_at: string;
}

// =====================================================
// VIEW INTERFACES (for API responses)
// =====================================================

export interface CurrentEmployeeStatus {
  user_id: string;
  name: string;
  email: string;
  department_name: string;
  department_id: string;
  task_description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  category?: TaskCategory;
  progress_percentage?: number;
  project_name?: string;
  expected_completion_date?: string;
  blocking_issues?: string;
  last_updated?: string;
  minutes_since_update?: number;
}

export interface DepartmentSummary {
  id: string;
  name: string;
  description?: string;
  total_employees: number;
  active_employees: number;
  idle_employees: number;
  offline_employees: number;
  urgent_tasks: number;
  blocked_tasks: number;
}

// =====================================================
// API REQUEST/RESPONSE INTERFACES
// =====================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  name: string;
  email: string;
  password: string;
  department_id: string;
}

export interface AuthResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
  expires_at: string;
}

export interface TaskUpdateRequest {
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage: number;
  project_id?: string;
  expected_completion_date?: string;
  blocking_issues?: string;
  tags?: string[]; // Tag names
}

export interface DashboardResponse {
  departments: DepartmentSummary[];
  total_employees: number;
  active_employees: number;
  urgent_tasks: number;
  blocked_tasks: number;
}

export interface DepartmentDetailResponse {
  department: Department;
  employees: CurrentEmployeeStatus[];
  projects: Project[];
  metrics: {
    productivity_score: number;
    completion_rate: number;
    avg_task_duration: number;
  };
}

// =====================================================
// ANALYTICS API INTERFACES
// =====================================================

export interface ProductivityAnalytics {
  user_id: string;
  user_name: string;
  department_name: string;
  period: string; // 'daily' | 'weekly' | 'monthly'
  data: {
    date: string;
    active_minutes: number;
    tasks_completed: number;
    productivity_score: number;
  }[];
}

export interface DepartmentAnalytics {
  department_id: string;
  department_name: string;
  period: string;
  data: {
    date: string;
    total_employees: number;
    active_employees: number;
    completion_rate: number;
    avg_productivity_score: number;
  }[];
}

export interface TaskCategoryAnalytics {
  category: TaskCategory;
  total_tasks: number;
  completed_tasks: number;
  completion_rate: number;
  avg_duration_minutes: number;
  trend: 'up' | 'down' | 'stable';
}

// =====================================================
// UTILITY TYPES
// =====================================================

export type CreateUserRequest = Omit<User, 'id' | 'created_at' | 'updated_at' | 'last_login_at'>;
export type UpdateUserRequest = Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>>;

export type CreateProjectRequest = Omit<Project, 'id' | 'created_at' | 'updated_at'>;
export type UpdateProjectRequest = Partial<Omit<Project, 'id' | 'created_at' | 'updated_at'>>;

// Enhanced task data interface (matches StatusUpdateForm)
export interface EnhancedTaskData {
  task: string;
  status: TaskStatus;
  priority: TaskPriority;
  estimatedDuration: string;
  taskCategory: TaskCategory;
  progressPercentage: number;
  relatedProject: string;
  blockingIssues: string;
  tags: TaskTag[];
  expectedCompletionDate: string;
}

// =====================================================
// ERROR INTERFACES
// =====================================================

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}
