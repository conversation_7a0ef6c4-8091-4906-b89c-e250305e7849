import { Request, Response } from 'express';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';
import { DashboardResponse, DepartmentDetailResponse } from '@/types/api';

// =====================================================
// DEPARTMENT CONTROLLERS
// =====================================================

// Get all departments with summary data for dashboard
export const getDepartments = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get department summary data
    const result = await query(`
      SELECT 
        d.id,
        d.name,
        d.description,
        COUNT(u.id) as total_employees,
        COUNT(CASE WHEN tu.status = 'active' THEN 1 END) as active_employees,
        COUNT(CASE WHEN tu.status = 'idle' THEN 1 END) as idle_employees,
        COUNT(CASE WHEN tu.status = 'offline' THEN 1 END) as offline_employees,
        ROUND(
          COUNT(CASE WHEN tu.status = 'active' THEN 1 END) * 100.0 / 
          NULLIF(COUNT(u.id), 0), 2
        ) as activity_percentage,
        COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
        COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks
      FROM departments d
      LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
      LEFT JOIN task_updates tu ON u.id = tu.user_id
      WHERE d.is_active = true
      GROUP BY d.id, d.name, d.description
      ORDER BY d.name
    `);

    // Get overall summary
    const summaryResult = await query(`
      SELECT 
        COUNT(DISTINCT u.id) as total_employees,
        COUNT(DISTINCT CASE WHEN tu.status = 'active' THEN u.id END) as active_employees,
        COUNT(DISTINCT d.id) as total_departments,
        COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
        COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks
      FROM departments d
      LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
      LEFT JOIN task_updates tu ON u.id = tu.user_id
      WHERE d.is_active = true
    `);

    const departments = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      total_employees: parseInt(row.total_employees) || 0,
      active_employees: parseInt(row.active_employees) || 0,
      idle_employees: parseInt(row.idle_employees) || 0,
      offline_employees: parseInt(row.offline_employees) || 0,
      urgent_tasks: parseInt(row.urgent_tasks) || 0,
      blocked_tasks: parseInt(row.blocked_tasks) || 0,
      activity_percentage: parseFloat(row.activity_percentage) || 0
    }));

    const summary = summaryResult.rows[0];

    const response: DashboardResponse = {
      departments,
      summary: {
        total_employees: parseInt(summary.total_employees) || 0,
        active_employees: parseInt(summary.active_employees) || 0,
        total_departments: parseInt(summary.total_departments) || 0,
        urgent_tasks: parseInt(summary.urgent_tasks) || 0,
        blocked_tasks: parseInt(summary.blocked_tasks) || 0
      }
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get departments error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get department details with employees
export const getDepartmentById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Get department info
    const deptResult = await query(`
      SELECT 
        d.*,
        u.name as manager_name
      FROM departments d
      LEFT JOIN users u ON d.manager_id = u.id
      WHERE d.id = $1 AND d.is_active = true
    `, [id]);

    if (deptResult.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Department not found'
      });
      return;
    }

    const department = deptResult.rows[0];

    // Get employees in department
    const employeesResult = await query(`
      SELECT 
        u.id,
        u.name,
        u.email,
        d.name as department,
        tu.task_description,
        tu.status,
        tu.priority,
        tu.category,
        tu.progress_percentage,
        p.name as project_name,
        tu.expected_completion_date,
        tu.blocking_issues,
        tu.updated_at,
        EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 as minutes_since_update,
        CASE 
          WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 < 30 THEN 'recent'
          WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 < 120 THEN 'moderate'
          ELSE 'stale'
        END as update_freshness
      FROM users u
      JOIN departments d ON u.department_id = d.id
      LEFT JOIN task_updates tu ON u.id = tu.user_id
      LEFT JOIN projects p ON tu.project_id = p.id
      WHERE u.department_id = $1 AND u.is_active = true
      ORDER BY u.name
    `, [id]);

    // Get projects in department
    const projectsResult = await query(`
      SELECT 
        p.id,
        p.name,
        p.client_name,
        p.status,
        p.priority,
        COUNT(tu.id) as active_tasks,
        AVG(tu.progress_percentage) as progress
      FROM projects p
      LEFT JOIN task_updates tu ON p.id = tu.project_id
      WHERE p.department_id = $1 AND p.is_active = true
      GROUP BY p.id, p.name, p.client_name, p.status, p.priority
      ORDER BY p.name
    `, [id]);

    // Get department metrics
    const metricsResult = await query(`
      SELECT 
        AVG(dps.productivity_score) as avg_productivity_score,
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) * 100.0 / 
          NULLIF(COUNT(th.id), 0) as completion_rate,
        AVG(th.session_duration_minutes) as avg_task_duration,
        SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) / 60.0 as total_active_hours
      FROM users u
      LEFT JOIN daily_productivity_summary dps ON u.id = dps.user_id 
        AND dps.date >= CURRENT_DATE - INTERVAL '30 days'
      LEFT JOIN task_history th ON u.id = th.user_id 
        AND th.created_at >= CURRENT_DATE - INTERVAL '30 days'
      WHERE u.department_id = $1 AND u.is_active = true
    `, [id]);

    const employees = employeesResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      task_description: row.task_description,
      status: row.status,
      priority: row.priority,
      category: row.category,
      progress_percentage: row.progress_percentage,
      project_name: row.project_name,
      expected_completion_date: row.expected_completion_date?.toISOString(),
      blocking_issues: row.blocking_issues,
      last_updated: row.updated_at?.toISOString(),
      minutes_since_update: Math.round(row.minutes_since_update) || 0,
      update_freshness: row.update_freshness
    }));

    const projects = projectsResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      client_name: row.client_name,
      status: row.status,
      priority: row.priority,
      active_tasks: parseInt(row.active_tasks) || 0,
      progress: Math.round(parseFloat(row.progress) || 0)
    }));

    const metrics = metricsResult.rows[0];

    const response: DepartmentDetailResponse = {
      department: {
        id: department.id,
        name: department.name,
        description: department.description,
        manager_name: department.manager_name
      },
      employees,
      projects,
      metrics: {
        productivity_score: Math.round(parseFloat(metrics.avg_productivity_score) || 0),
        completion_rate: Math.round(parseFloat(metrics.completion_rate) || 0),
        avg_task_duration: Math.round(parseFloat(metrics.avg_task_duration) || 0),
        total_active_hours: Math.round(parseFloat(metrics.total_active_hours) || 0)
      }
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get department by ID error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get employees in department
export const getDepartmentEmployees = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { search, status, priority } = req.query;

    let whereClause = 'WHERE u.department_id = $1 AND u.is_active = true';
    const params: any[] = [id];
    let paramCount = 1;

    // Add search filter
    if (search) {
      paramCount++;
      whereClause += ` AND (u.name ILIKE $${paramCount} OR u.email ILIKE $${paramCount} OR tu.task_description ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    // Add status filter
    if (status) {
      paramCount++;
      whereClause += ` AND tu.status = $${paramCount}`;
      params.push(status);
    }

    // Add priority filter
    if (priority) {
      paramCount++;
      whereClause += ` AND tu.priority = $${paramCount}`;
      params.push(priority);
    }

    const result = await query(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.role,
        tu.task_description,
        tu.status,
        tu.priority,
        tu.category,
        tu.progress_percentage,
        p.name as project_name,
        tu.expected_completion_date,
        tu.blocking_issues,
        tu.updated_at,
        EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 as minutes_since_update
      FROM users u
      LEFT JOIN task_updates tu ON u.id = tu.user_id
      LEFT JOIN projects p ON tu.project_id = p.id
      ${whereClause}
      ORDER BY u.name
    `, params);

    const employees = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      role: row.role,
      task_description: row.task_description,
      status: row.status,
      priority: row.priority,
      category: row.category,
      progress_percentage: row.progress_percentage,
      project_name: row.project_name,
      expected_completion_date: row.expected_completion_date?.toISOString(),
      blocking_issues: row.blocking_issues,
      last_updated: row.updated_at?.toISOString(),
      minutes_since_update: Math.round(row.minutes_since_update) || 0
    }));

    res.json({
      success: true,
      data: employees
    });
  } catch (error) {
    logger.error('Get department employees error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
