import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { config } from '@/config';
import { User } from '@/types/database';
import { logger, logAuth } from './logger';

// =====================================================
// JWT TOKEN UTILITIES
// =====================================================

export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  departmentId: string;
}

export interface RefreshTokenPayload {
  userId: string;
  tokenVersion: number;
}

// Generate access token
export const generateAccessToken = (user: User): string => {
  const payload: TokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    departmentId: user.department_id
  };

  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
    issuer: 'employee-dashboard',
    audience: 'employee-dashboard-users'
  } as jwt.SignOptions);
};

// Generate refresh token
export const generateRefreshToken = (userId: string, tokenVersion: number = 1): string => {
  const payload: RefreshTokenPayload = {
    userId,
    tokenVersion
  };

  return jwt.sign(payload, config.jwt.refreshSecret, {
    expiresIn: config.jwt.refreshExpiresIn,
    issuer: 'employee-dashboard',
    audience: 'employee-dashboard-refresh'
  } as jwt.SignOptions);
};

// Verify access token
export const verifyAccessToken = (token: string): TokenPayload | null => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret, {
      issuer: 'employee-dashboard',
      audience: 'employee-dashboard-users'
    }) as TokenPayload;

    return decoded;
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logAuth('token_verification_failed', undefined, { 
        error: error.message,
        tokenType: 'access'
      });
    }
    return null;
  }
};

// Verify refresh token
export const verifyRefreshToken = (token: string): RefreshTokenPayload | null => {
  try {
    const decoded = jwt.verify(token, config.jwt.refreshSecret, {
      issuer: 'employee-dashboard',
      audience: 'employee-dashboard-refresh'
    }) as RefreshTokenPayload;

    return decoded;
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logAuth('token_verification_failed', undefined, { 
        error: error.message,
        tokenType: 'refresh'
      });
    }
    return null;
  }
};

// Get token expiration date
export const getTokenExpiration = (token: string): Date | null => {
  try {
    const decoded = jwt.decode(token) as any;
    if (decoded && decoded.exp) {
      return new Date(decoded.exp * 1000);
    }
    return null;
  } catch (error) {
    return null;
  }
};

// =====================================================
// PASSWORD UTILITIES
// =====================================================

// Hash password
export const hashPassword = async (password: string): Promise<string> => {
  try {
    const salt = await bcrypt.genSalt(config.security.bcryptRounds);
    return await bcrypt.hash(password, salt);
  } catch (error) {
    logger.error('Password hashing failed', error);
    throw new Error('Password hashing failed');
  }
};

// Verify password
export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    logger.error('Password verification failed', error);
    return false;
  }
};

// Validate password strength
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (password.length > 128) {
    errors.push('Password must be less than 128 characters long');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Check for common weak passwords
  const commonPasswords = [
    'password', 'password123', '123456', '123456789', 'qwerty',
    'abc123', 'password1', 'admin', 'letmein', 'welcome'
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('Password is too common and easily guessable');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// =====================================================
// SESSION UTILITIES
// =====================================================

// Extract token from Authorization header
export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
};

// Generate session ID
export const generateSessionId = (): string => {
  return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// =====================================================
// RATE LIMITING UTILITIES
// =====================================================

// Generate rate limit key
export const generateRateLimitKey = (identifier: string, action: string): string => {
  return `rate_limit:${action}:${identifier}`;
};

// =====================================================
// SECURITY UTILITIES
// =====================================================

// Sanitize user data for logging
export const sanitizeUserForLogging = (user: Partial<User>) => {
  const { password_hash, ...sanitized } = user;
  return sanitized;
};

// Generate secure random string
export const generateSecureRandom = (length: number = 32): string => {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
};

// Validate email format
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate email domain for signup (only @imocha.io allowed)
export const validateEmailDomain = (email: string): boolean => {
  return email.toLowerCase().endsWith('@imocha.io');
};

// =====================================================
// PERMISSION UTILITIES
// =====================================================

// Check if user has permission for action
export const hasPermission = (userRole: string, requiredRole: string): boolean => {
  const roleHierarchy = {
    'admin': 3,
    'manager': 2,
    'employee': 1
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
};

// Check if user can access department
export const canAccessDepartment = (
  userRole: string, 
  userDepartmentId: string, 
  targetDepartmentId: string
): boolean => {
  // Admins can access all departments
  if (userRole === 'admin') {
    return true;
  }

  // Managers can access their own department
  if (userRole === 'manager' && userDepartmentId === targetDepartmentId) {
    return true;
  }

  // Employees can only access their own department
  if (userRole === 'employee' && userDepartmentId === targetDepartmentId) {
    return true;
  }

  return false;
};

// Check if user can modify user data
export const canModifyUser = (
  currentUserRole: string,
  currentUserId: string,
  targetUserId: string,
  targetUserRole: string
): boolean => {
  // Users can modify their own data
  if (currentUserId === targetUserId) {
    return true;
  }

  // Admins can modify anyone
  if (currentUserRole === 'admin') {
    return true;
  }

  // Managers can modify employees in their department (handled at route level)
  if (currentUserRole === 'manager' && targetUserRole === 'employee') {
    return true;
  }

  return false;
};
